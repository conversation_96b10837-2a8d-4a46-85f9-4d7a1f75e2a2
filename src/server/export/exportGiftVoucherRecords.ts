import dayjs from 'dayjs';
import { <PERSON>quest<PERSON>and<PERSON> } from 'express';
import { uniqBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { PeriodPayload } from '../../app/api/types';
import { RequestLocals } from '../core/express';
import { GiftVoucherModule, PasswordConfiguration } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { GiftVoucherPolicyAction } from '../permissions';
import { mainQueue } from '../queues/mainQueue';
import { getSessionDataFromRequest } from '../schema/session';
import getGiftVoucherRows from '../utils/excel/giftVouchers/getGiftVoucherRows';
import type { GiftVoucherResults } from '../utils/excel/giftVouchers/types/GiftVoucherResults';
import { uniqueObjectIds } from '../utils/fp';
import getApplicationFileName from '../utils/getApplicationFileName';
import { setHeaderColumnsWidth } from './exportApplications/shared';
import { getPassword, getPeriodFilter } from './utils';

type ExportGiftVouchersRequestBody = {
    companyIds: string[];
    period: PeriodPayload;
    nonce?: string;
};

// Equi-join to retrieve gift vouchers that match the given company IDs
// Project with attributes to be present in Excel
const lookupCompaniesFromGiftVoucher = (companyObjectIds: ObjectId[]) => [
    { $lookup: { from: 'modules', localField: 'moduleId', foreignField: '_id', as: 'module' } },
    { $unwind: { path: '$module', preserveNullAndEmptyArrays: false } },
    { $lookup: { from: 'companies', localField: 'module.companyId', foreignField: '_id', as: 'company' } },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: false } },
    {
        $project: {
            _id: 1,
            _versioning: 1,
            isDraft: 1,
            giftCode: 1,
            value: 1,
            purchaserId: 1,
            status: 1,
            purchasedDate: 1,
            moduleId: 1,
            numberOfBookingReferenceDays: 1,
            vehicleId: 1,
            companyId: '$company._id',
            stockId: 1,
            company: 1,
        },
    },
    { $unwind: '$companyId' },
    { $match: { companyId: { $in: uniqueObjectIds(companyObjectIds) } } },
];

const exportGiftVoucherRecords: RequestHandler<
    unknown,
    unknown,
    ExportGiftVouchersRequestBody,
    {},
    RequestLocals
> = async (req, res, next) => {
    const { companyIds, period, nonce: inputNonce } = req.body;

    const { getPermissionController, loaders, getTranslations } = res.locals.context;

    if (!companyIds || !period) {
        res.status(400).send('Bad request');

        return;
    }

    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();

    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const start: Date = period.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end: Date = period.end ? dayjs(period.end).endOf('day').toDate() : null;
    const giftVoucherPermission = await permissions.giftVouchers.getFilterQueryForAction(GiftVoucherPolicyAction.View);
    const companyObjectIds = companyIds.map(companyId => new ObjectId(companyId));

    const pipeline = [
        ...lookupCompaniesFromGiftVoucher(companyObjectIds),
        {
            $match: giftVoucherPermission,
        },
        {
            $match: {
                $and: [getPeriodFilter(start, end), { isDraft: false, '_versioning.isLatest': true }],
            },
        },
    ];

    const results = await collections.giftVouchers.aggregate(pipeline).toArray();
    const modulePromises = results.map(result => loaders.moduleById.load(result.moduleId));
    const modules = (await Promise.all(modulePromises)) as GiftVoucherModule[];

    const companies = await collections.companies
        .find({ _id: { $in: uniqueObjectIds(results.map(result => result.companyId)) } })
        .toArray();

    // modules passed below are constrained to the same company
    const company = companies?.[0];
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    try {
        const rows = await getGiftVoucherRows(
            results as GiftVoucherResults[],
            uniqBy('_id', modules),
            {
                currencyCode: companies.length > 1 ? undefined : company.currency,
                timeZone: companies.length > 1 ? undefined : company.timeZone,
            },
            loaders,
            getTranslations
        );

        const sheetName = 'Default Worksheet';

        const workbook = await XlsxPopulate.fromBlankAsync();
        const worksheet = workbook.sheet(0).name(sheetName);
        worksheet.cell('A1').value(rows);
        setHeaderColumnsWidth(worksheet, rows[0]);

        const nonce = inputNonce ?? nanoid();
        const password = await getPassword(nonce);

        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;
        const filename = encodeURI(getApplicationFileName(company.legalName.defaultValue, period));

        res.set({
            ...(isPasswordProtected ? { 'X-EXCEL-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

        const date = new Date();

        // Send email to user
        if (isPasswordProtected) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                applicationType: 'Gift Voucher',
                documentType: 'Excel',
                password,
                date,
                company,
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportGiftVoucherRecords;
