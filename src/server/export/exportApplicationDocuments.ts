import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { RequestLocals } from '../core/express';
import { getFileStream } from '../core/storage';
import { ApplicationDocument, ApplicationDocumentKind, ApplicationStage, PasswordConfiguration } from '../database';
import { ApplicationStage as Stage } from '../database/documents/Applications/core';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { getApplicationIdentifier } from '../utils/application';
import getEncryptedZip from '../utils/getEncryptedZip';
import { getApplicationType, getBEApplicationStage } from './exportApplications';

type Param = {
    applicationId: string;
};

type Query = {
    stage: Stage;
};

const identityUploads = [
    ApplicationDocumentKind.CustomerIdentity,
    ApplicationDocumentKind.IdentityCardFront,
    ApplicationDocumentKind.IdentityCardBack,
    ApplicationDocumentKind.KYCUaeIdentityUpload,
    ApplicationDocumentKind.KYCSalaryTransferredBankUpload,
    ApplicationDocumentKind.KYCIdentityUpload,
    ApplicationDocumentKind.KYCLicenseUpload,
    ApplicationDocumentKind.KYCPassportUpload,
];

const otherAttachmentUploads = [
    ApplicationDocumentKind.OtherAttachment,
    ApplicationDocumentKind.KYCOtherDocumentUpload,
];

// Need to filter out which documents will be exported
// refer to UI side: src/app/pages/shared/ApplicationDetailsPage/standard/DocumentTab/index.tsx
const filterDocumentToExport = (doc: ApplicationDocument, stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing: {
            return [
                ApplicationDocumentKind.Agreement,
                ...identityUploads,
                ApplicationDocumentKind.CorporateIdentity,
                ApplicationDocumentKind.GuarantorIdentity,
                ApplicationDocumentKind.ApprovalAdvice,
                ApplicationDocumentKind.ReleaseLetter,
                ApplicationDocumentKind.InsuranceCoverNote,
                ApplicationDocumentKind.LogCard,
                ApplicationDocumentKind.Invoice,
                ApplicationDocumentKind.VSOUpload,
                ...otherAttachmentUploads,
            ].includes(doc.kind);
        }

        case ApplicationStage.Insurance: {
            return [ApplicationDocumentKind.InsuranceAgreement, ...identityUploads, ...otherAttachmentUploads].includes(
                doc.kind
            );
        }

        case ApplicationStage.Mobility: {
            return [
                ApplicationDocumentKind.Agreement,
                ApplicationDocumentKind.CustomerDLCopy,
                ApplicationDocumentKind.CustomerEIDPassportCopy,
                ApplicationDocumentKind.KYCIdentityUpload,
                ApplicationDocumentKind.KYCLicenseUpload,
                ApplicationDocumentKind.OtherAttachment,
            ].includes(doc.kind);
        }

        case ApplicationStage.Reservation:
        case ApplicationStage.Lead: {
            return [
                ApplicationDocumentKind.KYCIdentityUpload,
                ApplicationDocumentKind.KYCLicenseUpload,
                ApplicationDocumentKind.KYCPassportUpload,
                ApplicationDocumentKind.KYCOtherDocumentUpload,
            ].includes(doc.kind);
        }

        case ApplicationStage.Appointment: {
            return [ApplicationDocumentKind.TestDriveAgreement];
        }

        default:
            return true;
    }
};

const exportApplicationDocuments: RequestHandler<Param, unknown, unknown, Query, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { getPermissionController, getUser } = res.locals.context;
    const permissions = await getPermissionController();

    const { applicationId: inputApplicationId } = req.params;
    const { stage: inputStage } = req.query;
    const { collections } = await getDatabaseContext();

    const user = await getUser();

    const stage = getBEApplicationStage(inputStage);

    if (!ObjectId.isValid(inputApplicationId) || !stage) {
        res.status(400).send('Bad request');

        return;
    }

    const applicationId = new ObjectId(inputApplicationId);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View, [
        stage,
    ]);

    const application = await collections.applications.findOne({
        $and: [{ _id: applicationId, '_versioning.isLatest': true }, applicationPermission],
    });

    if (!application) {
        res.status(404).send('Application not found.');

        return;
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    if (!lead) {
        res.status(404).send('Lead not found.');

        return;
    }

    try {
        const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
        const company = applicationModule?.companyId
            ? await collections.companies.findOne({ _id: applicationModule.companyId })
            : null;

        const password = nanoid();

        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

        res.set({
            ...(isPasswordProtected ? { 'X-DOWNLOAD-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="documents.zip"`,
            'Content-Type': 'application/zip',
        });

        // Need to filter out which documents will be exported
        // refer to UI side: src/app/pages/shared/ApplicationDetailsPage/standard/DocumentTab/index.tsx
        const filteredOutDocuments = application.documents.filter(doc => filterDocumentToExport(doc, stage));

        const files = await Promise.all(
            filteredOutDocuments.map(async document => {
                const source = await getFileStream(document);

                return { source, filename: document.filename };
            })
        );

        const encryptedZip = await getEncryptedZip(files, isPasswordProtected ? password : undefined);

        const identifier = getApplicationIdentifier(application, lead, stage);

        if (isPasswordProtected) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date: new Date(),
                company,
                applicationType: [stage ? getApplicationType(stage) : 'Application', identifier].join(' '),
                documentType: 'Documents',
                user,
            });
        }

        res.send(encryptedZip);
    } catch (error) {
        next(error);
    }
};

export default exportApplicationDocuments;
