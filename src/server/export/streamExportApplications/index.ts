import dayjs from 'dayjs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { RequestLocals } from '../../core/express';
import { ApplicationModule } from '../../database';
import { ApplicationStage as Stage } from '../../database/documents/Applications/core';
import getDatabaseContext from '../../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../../permissions';
import { mainQueue } from '../../queues';
import { getSessionDataFromRequest } from '../../schema/session';
import { getApplicationByCapFormat, getApplicationBySystemFormat } from '../../utils/applicationExportQueries';
import { FormatCapPurpose } from '../../utils/excel/applications/cap/types';
import { ExcelExportFormat } from '../../utils/excel/enums';
import { uniqueObjectIds } from '../../utils/fp';
import { getBEApplicationStage } from '../exportApplications';
import { PeriodPayload } from '../type';
import { validCapPurposes } from '../utils';

type ExportApplicationBody = {
    moduleIds: string[];
    eventId?: string;
    dealerIds: string[];
    stage: Stage;
    period?: PeriodPayload;
    format: ExcelExportFormat.system | ExcelExportFormat.cap;
    capPurpose?: FormatCapPurpose[];
    nonce?: string;
    languageId?: string;
    filename?: string[];
};

/**
 * Queue a background job to export applications and send the result via email
 */
const streamExportApplications: RequestHandler<unknown, unknown, ExportApplicationBody, {}, RequestLocals> = async (
    req,
    res,
    next
) => {
    try {
        const { getPermissionController } = res.locals.context;

        const {
            moduleIds: inputModuleIds,
            eventId,
            dealerIds: inputDealerIds,
            period,
            stage: inputStage,
            format,
            capPurpose,
            nonce: inputNonce,
            languageId,
            filename,
        } = req.body;

        // Validate format first, with c@p format, purpose must be provided
        if (format === 'cap') {
            if (!capPurpose || !Array.isArray(capPurpose) || capPurpose.length === 0) {
                res.status(400).send('Bad request: CAP format requires purpose array');

                return;
            }

            // Validate all purposes in the array
            if (capPurpose.some(purpose => !validCapPurposes.includes(purpose))) {
                res.status(400).send('Bad request: Invalid CAP purpose in array');

                return;
            }
        }

        if (
            (!eventId || !ObjectId.isValid(eventId)) &&
            (!(inputModuleIds.length > 0) ||
                inputModuleIds.some(inputModuleId => !ObjectId.isValid(inputModuleId)) ||
                inputDealerIds.some(dealerId => !ObjectId.isValid(dealerId)))
        ) {
            res.status(400).send('Bad request');

            return;
        }

        // Validate stage
        const stage = getBEApplicationStage(inputStage);

        if (!stage) {
            res.status(400).send('Bad request');

            return;
        }

        const { collections } = await getDatabaseContext();

        const permissions = await getPermissionController();

        const userToken = req.headers.Authorization as string;
        const session = await getSessionDataFromRequest(req, userToken);
        const { userId } = session;

        const moduleIds = inputModuleIds.map(inputModuleId => new ObjectId(inputModuleId));
        const dealerIds = inputDealerIds.map(dealerId => new ObjectId(dealerId));

        const modules = (await collections.modules.find({ _id: { $in: moduleIds } }).toArray()) as ApplicationModule[];
        if (modules.length <= 0 && !eventId) {
            res.status(404).send('No modules found');

            return;
        }

        const companies = await collections.companies
            .find({ _id: { $in: uniqueObjectIds(modules.map(module => module.companyId)) } })
            .toArray();

        // modules passed below are constrained to the same company
        const company = companies?.[0];
        if (!company) {
            res.status(404).send('Company not found');

            return;
        }

        // Get applications
        const start: Date = period.start ? dayjs(period.start).startOf('day').toDate() : null;
        const end: Date = period.end ? dayjs(period.end).endOf('day').toDate() : null;

        const applicationPermission = await permissions.applications.getFilterQueryForAction(
            ApplicationPolicyAction.View,
            [stage]
        );

        const arcApplicationModules = modules;

        // Use the shared query functions from applicationExportQueries.ts
        const applications = await (format === 'cap'
            ? getApplicationByCapFormat({
                  collections,
                  applicationPermission,
                  modules: arcApplicationModules,
                  dealerIds,
                  stage,
                  start,
                  end,
              })
            : getApplicationBySystemFormat({
                  collections,
                  applicationPermission,
                  modules: arcApplicationModules,
                  eventId,
                  dealerIds,
                  stage,
                  start,
                  end,
              }));

        // Check if applications is empty
        const isEmpty = Array.isArray(applications) ? applications.length === 0 : !(await applications.hasNext());

        if (isEmpty) {
            res.status(204).end();

            return;
        }

        // Queue the job with validated data
        const nonce = inputNonce ?? nanoid();

        await mainQueue.add({
            type: 'processApplicationExport',
            userId,
            moduleIds: inputModuleIds,
            eventId,
            dealerIds: inputDealerIds,
            stage,
            period,
            format,
            capPurpose,
            nonce,
            languageId,
            filename,
        });

        res.status(200).send('OK');
    } catch (error) {
        console.error('Error in streamExportApplications:', error);

        if (!res.headersSent) {
            res.status(500).send(`Error queuing export job: ${error.message || 'Unknown error'}`);
        } else {
            next(error);
        }
    }
};

export default streamExportApplications;
