import dayjs from 'dayjs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { RequestLocals } from '../../core/express';
import type { Application } from '../../database/documents/Applications';
import { ApplicationStage } from '../../database/documents/Applications/core';
import type {
    ApplicationResubmittedToBankAuditTrail,
    ApplicationResubmittedToSystemAuditTrail,
    ApplicationSubmittedToBankAuditTrail,
    ApplicationSubmittedToSystemAuditTrail,
} from '../../database/documents/AuditTrail/types/applications';
import { PasswordConfiguration } from '../../database/documents/Company';
import type { ApplicationModule } from '../../database/documents/modules';
import getDatabaseContext from '../../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../../permissions';
import { mainQueue } from '../../queues';
import { AuditTrailKind } from '../../schema/resolvers/enums';
import { getSessionDataFromRequest } from '../../schema/session';
import { getPathByStage } from '../../utils/application';
import getExcelApplicationRows from '../../utils/excel/applications';
import { FormatCapPurpose } from '../../utils/excel/applications/cap/types';
import { ExcelExportFormat } from '../../utils/excel/enums';
import { uniqueObjectIds } from '../../utils/fp';
import getApplicationFileName from '../../utils/getApplicationFileName';
import { PeriodPayload } from '../type';
import { getPassword, getPeriodFilter, validCapPurposes } from '../utils';
import { setHeaderColumnsWidth } from './shared';

type ExportApplicationBody = {
    moduleIds: string[];
    dealerIds: string[];
    stage: ApplicationStage;
    period?: PeriodPayload;
    format: 'system' | 'cap';
    capPurpose?: FormatCapPurpose;
    nonce?: string;
    languageId?: string;
};

export const getBEApplicationStage = (stage: ApplicationStage): ApplicationStage => {
    console.log(
        '%cMyProject%cline:44%cstage',
        'color:#fff;background:#ee6f57;padding:3px;border-radius:2px',
        'color:#fff;background:#1f3c88;padding:3px;border-radius:2px',
        'color:#fff;background:rgb(89, 61, 67);padding:3px;border-radius:2px',
        stage,
        stage === ApplicationStage.Appointment
    );
    switch (stage) {
        case ApplicationStage.Financing:
            return ApplicationStage.Financing;

        case ApplicationStage.Lead:
            return ApplicationStage.Lead;

        case ApplicationStage.Reservation:
            return ApplicationStage.Reservation;

        case ApplicationStage.Appointment:
            return ApplicationStage.Appointment;

        case ApplicationStage.Insurance:
            return ApplicationStage.Insurance;

        case ApplicationStage.Mobility:
            return ApplicationStage.Mobility;

        case ApplicationStage.VisitAppointment:
            return ApplicationStage.VisitAppointment;

        default:
            throw new Error(`Invalid stage from getting BE application stage: ${stage}`);
    }
};

export const getApplicationType = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing:
            return 'Application';

        case ApplicationStage.Lead:
            return 'Lead';

        case ApplicationStage.Reservation:
            return 'Reservation';

        case ApplicationStage.Mobility:
            return 'Mobility';

        case ApplicationStage.Appointment:
            return 'Test Drive';

        case ApplicationStage.Insurance:
            return 'Insurance';

        case ApplicationStage.VisitAppointment:
            return 'Showroom Visit';

        default:
            throw new Error('Invalid stage from getting application type');
    }
};

const exportApplications: RequestHandler<unknown, unknown, ExportApplicationBody, {}, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { getPermissionController, loaders } = res.locals.context;

    const {
        moduleIds: inputModuleIds,
        dealerIds: inputDealerIds,
        period,
        stage: inputStage,
        format,
        capPurpose,
        nonce: inputNonce,
        languageId,
    } = req.body;

    // Validate format first, with c@p format, purpose must be provided
    if (format === ExcelExportFormat.cap && (!isString(capPurpose) || !validCapPurposes.includes(capPurpose))) {
        res.status(400).send('Bad request');

        return;
    }

    // Validate input module and input dealers
    if (
        !(inputModuleIds.length > 0) ||
        inputModuleIds.some(inputModuleId => !ObjectId.isValid(inputModuleId)) ||
        inputDealerIds.some(dealerId => !ObjectId.isValid(dealerId))
    ) {
        res.status(400).send('Bad request');

        return;
    }

    // Validate stage
    const stage = getBEApplicationStage(inputStage);

    if (!stage) {
        res.status(400).send('Bad request');

        return;
    }

    const { collections } = await getDatabaseContext();

    const permissions = await getPermissionController();

    // get user from token
    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const moduleIds = inputModuleIds.map(inputModuleId => new ObjectId(inputModuleId));
    const dealerIds = inputDealerIds.map(dealerId => new ObjectId(dealerId));

    const modules = (await collections.modules.find({ _id: { $in: moduleIds } }).toArray()) as ApplicationModule[];
    if (modules.length <= 0) {
        res.status(404).send('Not found');

        return;
    }
    const companies = await collections.companies
        .find({ _id: { $in: uniqueObjectIds(modules.map(module => module.companyId)) } })
        .toArray();

    // modules passed below are constrained to the same company
    const company = companies?.[0];
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    // Get applications
    const start: Date = period.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end: Date = period.end ? dayjs(period.end).endOf('day').toDate() : null;

    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View, [
        stage,
    ]);

    const arcApplicationModules = modules;

    const path = getPathByStage(stage);
    const moreFilter = [
        ApplicationStage.Financing,
        ApplicationStage.Insurance,
        ApplicationStage.VisitAppointment,
        ApplicationStage.Appointment,
    ].includes(stage) && {
        $or: [
            { [`${path}.isDraft`]: { $exists: false } },
            { [`${path}.isDraft`]: { $exists: true }, [`${path}.isDraft`]: false },
        ],
    };

    // System format application getter
    const getApplicationBySystemFormat = async () => {
        // Get initial versioning suiteIds first
        const applicationSuiteIds = await collections.applications
            .find({
                $and: [
                    applicationPermission,
                    {
                        $or: [
                            arcApplicationModules.length && {
                                stages: { $in: [stage] },
                                moduleId: { $in: arcApplicationModules.map(module => module._id) },
                                dealerId: { $in: dealerIds },
                                ...getPeriodFilter(start, end),
                                isDraft: false,
                                '_versioning.isLatest': true,
                                ...moreFilter,
                            },
                        ].filter(Boolean),
                    },
                ],
            })
            .map((application: Application) => application._versioning.suiteId)
            .toArray();

        // Get applications by audit trail
        const auditTrailInternalApplicationIds =
            stage !== ApplicationStage.Mobility
                ? await collections.auditTrails
                      .find({
                          ...(start && { _date: { $gte: start } }),
                          _kind: {
                              // For internal would be submission to system, except for mobility
                              $in: [
                                  AuditTrailKind.ApplicationSubmittedToSystem,
                                  AuditTrailKind.ApplicationResubmittedToSystem,
                              ],
                          },
                          stages: { $in: [stage] },
                          applicationSuiteId: { $in: applicationSuiteIds },
                      })
                      .map(
                          (
                              auditTrail:
                                  | ApplicationSubmittedToSystemAuditTrail
                                  | ApplicationResubmittedToSystemAuditTrail
                          ) => auditTrail.applicationId
                      )
                      .toArray()
                : [];

        const auditTrailExternalApplicationIds = await collections.auditTrails
            .find({
                ...(start && { _date: { $gte: start } }),
                _kind: {
                    // For external would be submission to bank
                    $in: [AuditTrailKind.ApplicationResubmittedToBank, AuditTrailKind.ApplicationSubmittedToBank],
                },
                stages: { $in: [stage] },
                applicationSuiteId: { $in: applicationSuiteIds },
            })
            .map(
                (auditTrail: ApplicationSubmittedToBankAuditTrail | ApplicationResubmittedToBankAuditTrail) =>
                    auditTrail.applicationId
            )
            .toArray();

        // Get final result
        const results = await collections.applications
            .find({
                $and: [
                    applicationPermission,
                    {
                        // Using the same filter with, inside $or to filter out by submission audit trail
                        $or: [
                            arcApplicationModules.length && {
                                stages: { $in: [stage] },
                                moduleId: { $in: arcApplicationModules.map(module => module._id) },
                                isDraft: false,
                                dealerId: { $in: dealerIds },
                                ...getPeriodFilter(start, end),
                                '_versioning.suiteId': { $in: applicationSuiteIds },
                                ...moreFilter,

                                $or: [
                                    // AN-1561: we currently don't allow insurance resubmission
                                    stage !== ApplicationStage.Insurance && { '_versioning.isLatest': true },

                                    // For mobility, no need to retrieve it by audit trail
                                    // As it only want to have 1 record inside excel report
                                    stage !== ApplicationStage.Mobility && {
                                        _id: { $in: auditTrailInternalApplicationIds },
                                    },
                                ].filter(Boolean),
                            },
                        ].filter(Boolean),
                    },
                ],
            })
            .toArray();

        return results;
    };

    // Cap format application getter
    const getApplicationByCapFormat = async () => {
        const result = await collections.applications
            .find({
                $and: [
                    applicationPermission,
                    {
                        $or: [
                            arcApplicationModules.length && {
                                stages: { $in: [stage] },
                                moduleId: { $in: arcApplicationModules.map(module => module._id) },
                                isDraft: false,
                                dealerId: { $in: dealerIds },
                                ...getPeriodFilter(start, end),
                                ...moreFilter,
                            },
                        ].filter(Boolean),
                    },
                ],
            })
            .toArray();

        return result;
    };

    const applications =
        format === ExcelExportFormat.cap ? await getApplicationByCapFormat() : await getApplicationBySystemFormat();

    if (!applications.length) {
        res.status(404).send('No applications found in the within selected time frame');

        return;
    }

    try {
        const rows = await getExcelApplicationRows(
            applications,
            modules,
            format === ExcelExportFormat.cap
                ? {
                      format: ExcelExportFormat.cap,
                      capPurpose,
                      tenant: `${company.displayName}_${company.countryCode}`.toUpperCase(),
                      stage,
                      routerFirstLanguage: languageId || null,
                      timeZone: company?.timeZone,
                  }
                : {
                      format: ExcelExportFormat.system,
                      currencyCode: companies.length > 1 ? undefined : company.currency,
                      timeZone: companies.length > 1 ? undefined : company.timeZone,
                      stage,
                      routerFirstLanguage: languageId || null,
                  },
            loaders
        );

        const sheetName = 'Default Worksheet';

        const workbook = await XlsxPopulate.fromBlankAsync();
        const worksheet = workbook.sheet(0).name(sheetName);
        worksheet.cell('A1').value(rows);
        setHeaderColumnsWidth(worksheet, rows[0]);

        const nonce = inputNonce ?? nanoid();
        const password = await getPassword(nonce);

        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

        const filename = encodeURI(
            getApplicationFileName(company.legalName.defaultValue, period, inputStage, capPurpose)
        );

        res.set({
            ...(isPasswordProtected ? { 'X-EXCEL-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

        const date = new Date();

        // Send email to user
        if (isPasswordProtected) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: stage ? `${getApplicationType(stage)}s` : 'Applications',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportApplications;
