import dayjs from 'dayjs';
import { RequestHandler } from 'express';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { RequestLocals } from '../core/express';
import { ApplicationModule, PasswordConfiguration } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import getExcelTradeInRows from '../utils/excel/tradeIns';
import { uniqueObjectIds } from '../utils/fp';
import { getPeriodString } from '../utils/getApplicationFileName';
import { setHeaderColumnsWidth } from './exportApplications/shared';
import { PeriodPayload } from './type';
import { getPassword } from './utils';

type ExportTradeInsBody = {
    moduleIds: string[];
    period?: PeriodPayload;
    nonce?: string;
};

const exportTradeIns: RequestHandler<unknown, unknown, ExportTradeInsBody, {}, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { moduleIds: inputModuleIds, period, nonce: inputNonce } = req.body;

    // Validate input module and input dealers
    if (!(inputModuleIds.length > 0) || inputModuleIds.some(inputModuleId => !ObjectId.isValid(inputModuleId))) {
        res.status(400).send('Bad request');

        return;
    }

    const { collections } = await getDatabaseContext();

    // get user from token
    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const moduleIds = inputModuleIds.map(inputModuleId => new ObjectId(inputModuleId));

    const modules = (await collections.modules.find({ _id: { $in: moduleIds } }).toArray()) as ApplicationModule[];
    if (modules.length <= 0) {
        res.status(404).send('Not found');

        return;
    }
    const companies = await collections.companies
        .find({ _id: { $in: uniqueObjectIds(modules.map(module => module.companyId)) } })
        .toArray();

    // modules passed below are constrained to the same company
    const company = companies?.[0];
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    // Get applications
    const start: Date = period.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end: Date = period.end ? dayjs(period.end).endOf('day').toDate() : null;

    const tradeIns = await collections.tradeIns
        .find({
            moduleId: { $in: moduleIds },
            ...((start || end) && {
                '_versioning.createdAt': {
                    ...(start && { $gte: start }),
                    ...(end && { $lte: end }),
                },
            }),
        })
        .toArray();

    if (!tradeIns.length) {
        res.status(404).send('No trade ins found in the within selected time frame');

        return;
    }

    try {
        const rows = await getExcelTradeInRows(tradeIns, { timeZone: company.timeZone });

        const sheetName = 'Default Worksheet';

        const workbook = await XlsxPopulate.fromBlankAsync();
        const worksheet = workbook.sheet(0).name(sheetName);
        worksheet.cell('A1').value(rows);
        setHeaderColumnsWidth(worksheet, rows[0]);

        const nonce = inputNonce ?? nanoid();
        const password = await getPassword(nonce);

        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

        const filename = encodeURI(`${company.legalName.defaultValue}_TradeIn_${getPeriodString(period)}.xlsx`);

        res.set({
            ...(isPasswordProtected ? { 'X-EXCEL-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

        const date = new Date();

        // Send email to user
        if (isPasswordProtected) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: 'TradeIns',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportTradeIns;
