import type { TFunction } from 'i18next';
import { type Dictionary } from 'lodash';
import { groupBy, keyBy } from 'lodash/fp';
import type { WithId } from 'mongodb';
import {
    type AllowedApplicationForFinancing,
    type AllowedApplicationForInsurance,
    type Application,
    type ApplicationApprovedAuditTrail,
    type ApplicationDeclinedAuditTrail,
    type ApplicationHasPromoCode,
    type ApplicationJourney,
    ApplicationKind,
    type ApplicationModule,
    ApplicationResubmittedToBankAuditTrail,
    type ApplicationResubmittedToSystemAuditTrail,
    ApplicationStage,
    ApplicationStatus,
    type ApplicationSubmittedToBankAuditTrail,
    type ApplicationSubmittedToSystemAuditTrail,
    AuditTrailKind,
    type Author,
    AuthorKind,
    type Bank,
    BankKind,
    type BookingAmendedAuditTrail,
    type BookingSubmittedAuditTrail,
    type Company,
    type ConsentsAndDeclarations,
    ConsentsAndDeclarationsType,
    type Customer,
    DataField,
    type Dealer,
    type Insurer,
    type Lead,
    type LocalCustomerManagementModule,
    type LocalMake,
    type LocalModel,
    type LocalVariant,
    type MobilityApplication,
    ModuleType,
    type User,
    type Vehicle,
    VehicleKind,
    getCustomerFullName,
    getKYCPresetsForCustomerModule,
    getLocalCustomerAggregatedFields,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { hasAppointmentScenario } from '../../../../journeys/common/helpers';
import createLoaders, { Loaders } from '../../../../loaders';
import {
    getApplicationAssigneeId,
    getApplicationStage,
    getApplicationStages,
    getPathByStage,
} from '../../../application';
import ensureManyFromLoaders from '../../../ensureManyFromLoaders';
import { uniqueObjectIds } from '../../../fp';
import { CheckConsent } from '../../enums';
import {
    type BankSupportedApplication,
    type DealerSupportedApplication,
    type KycSupportedApplication,
    type PreparedSystemApplicationData,
    type SystemSupportedApplicationModule,
    type VehicleSupportedApplication,
} from '../shared/types';

export const customerByIdFromApplications = async (applications: KycSupportedApplication[], loaders: Loaders) => {
    const customerIds = uniqueObjectIds(
        applications
            .flatMap(item => {
                if (!item) {
                    return [];
                }

                const creatorCustomer =
                    item._versioning.createdBy?.kind === AuthorKind.Customer ? [item._versioning.createdBy.id] : [];
                const editorCustomer =
                    item._versioning.updatedBy?.kind === AuthorKind.Customer ? [item._versioning.updatedBy.id] : [];

                return [...creatorCustomer, ...editorCustomer, item.applicantId];
            })
            .filter(Boolean)
    );

    if (!customerIds.length) {
        return {};
    }

    // Retrieve customer by ids
    const customers = await loaders.customerById.loadMany(customerIds).then(ensureManyFromLoaders);
    const customerModuleIds = uniqueObjectIds(customers.map(customer => customer.moduleId));

    const customerModules = (await loaders.moduleById
        .loadMany(customerModuleIds)
        .then(ensureManyFromLoaders)) as LocalCustomerManagementModule[];

    // Suite ids is required as well, for author
    // Because as creator of application, there is no data yet for the customer
    const customerSuiteIds = uniqueObjectIds(customers.map(item => item._versioning.suiteId));
    const customersLatest = (
        await loaders.customerByLatestSuiteId.loadMany(customerSuiteIds).then(ensureManyFromLoaders)
    ).filter(Boolean);

    return {
        // Key by customer id
        // prevent excel export failing when customer is deleted
        customerById: keyBy(item => item._id.toHexString(), customers.filter(Boolean)),

        // Key by customer Module ID
        customerModuleByModuleId: keyBy(item => item._id.toHexString(), customerModules.filter(Boolean)),

        // Key by suite id, the latest customer data
        customerByLatestSuiteId: keyBy(item => item._versioning.suiteId.toHexString(), customersLatest.filter(Boolean)),
    };
};

export const userByIdFromApplications = async (applications: Application[], loaders: Loaders) => {
    const allUsersInApplications = uniqueObjectIds(
        applications
            .flatMap(item => [
                ...getApplicationStages(item)
                    .map(({ value }) => value?.assigneeId)
                    .filter(Boolean),
                item._versioning.createdBy?.kind === AuthorKind.User ? item._versioning.createdBy.id : undefined,
                item._versioning.updatedBy?.kind === AuthorKind.User ? item._versioning.updatedBy.id : undefined,
            ])
            .filter(Boolean)
    );

    if (!allUsersInApplications.length) {
        return {};
    }

    const users = await loaders.userById.loadMany(allUsersInApplications).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), users);
};

export const leadByIdFromApplications = async (applications: Application[], loaders: Loaders) => {
    const leadIds = uniqueObjectIds(applications.map(application => application.leadId).filter(Boolean));

    if (!leadIds.length) {
        return {};
    }

    const leads = await loaders.leadById.loadMany(leadIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), leads);
};

export const dealerByIdFromApplications = async (applications: DealerSupportedApplication[], loaders: Loaders) => {
    const dealerIds = uniqueObjectIds(applications.map(item => item.dealerId).filter(Boolean));

    const dealers = await loaders.dealerById.loadMany(dealerIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), dealers);
};

export const companyByIdFromDealer = async (dealer: Dealer, loaders: Loaders) => {
    const company = await loaders.companyById.load(dealer.companyId);

    return company;
};

export const vehicleByIdFromApplications = async (applications: VehicleSupportedApplication[], loaders: Loaders) => {
    const vehicleIds = uniqueObjectIds(applications.map(item => item.vehicleId).filter(Boolean));

    const vehicles = await loaders.vehicleById.loadMany(vehicleIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), vehicles);
};

export const journeyByApplicationSuiteIdFromApplications = async (applications: Application[], loaders: Loaders) => {
    const applicationSuiteIds = uniqueObjectIds(applications.map(item => item._versioning?.suiteId).filter(Boolean));

    if (!applicationSuiteIds.length) {
        return {
            journeyByApplicationSuiteId: {},
            journeys: [],
        };
    }

    const applicationJourneys = await loaders.applicationJourneyBySuiteId
        .loadMany(applicationSuiteIds)
        .then(ensureManyFromLoaders);

    // If we have double key in the record, then `keyBy` will override the previous one
    return {
        journeyByApplicationSuiteId: keyBy(item => item.applicationSuiteId.toHexString(), applicationJourneys),
        journeys: applicationJourneys,
    };
};

export const modelMakeByVariantIdFromApplications = async (
    applications: VehicleSupportedApplication[],
    loaders: Loaders
) => {
    const vehicleIds = uniqueObjectIds(applications.map(item => item.vehicleId).filter(Boolean));
    if (!vehicleIds.length) {
        return {};
    }

    const loadedVariants = await loaders.vehicleById.loadMany(vehicleIds).then(ensureManyFromLoaders<LocalVariant>);
    const variants = loadedVariants.filter(item => item._kind === VehicleKind.LocalVariant);

    if (!variants.length) {
        return {};
    }

    const vehicleModelIds = uniqueObjectIds(variants.flatMap(item => [item.modelId, item.submodelId]).filter(Boolean));
    const models = await loaders.vehicleById.loadMany(vehicleModelIds).then(ensureManyFromLoaders<LocalModel>);

    const modelMakeIds = uniqueObjectIds(models.map(item => item.makeId));
    const makes = await loaders.vehicleById.loadMany(modelMakeIds).then(ensureManyFromLoaders<LocalMake>);

    const modelById = keyBy(item => item._id.toHexString(), models);
    const makeById = keyBy(item => item._id.toHexString(), makes);

    return variants.reduce(
        (acc, variant) => {
            const model = modelById[variant.modelId.toHexString()] ?? null;

            return {
                ...acc,
                [variant._id.toHexString()]: {
                    model,
                    subModel: variant.submodelId ? modelById[variant.submodelId.toHexString()] : null,
                    make: model?.makeId ? makeById[model.makeId.toHexString()] : null,
                },
            };
        },
        {} as {
            [vehicleId: string]: {
                model: LocalModel | null;
                subModel: LocalModel | null;
                make: LocalMake | null;
            };
        }
    );
};

export const referenceApplicationsByApplicationsSuiteId = async (applications: Application[]) => {
    const { collections } = await getDatabaseContext();

    const referencesApplication = (await collections.applications
        .find({ '_versioning.suiteId': { $in: applications.map(application => application._versioning.suiteId) } })
        .toArray()) as Application[];

    const applicationId = groupBy(item => item._versioning.suiteId.toHexString(), referencesApplication);

    return applicationId;
};

const applicationApprovalByApplicationSuiteIdFromApplications = async (
    applications: Application[],
    stage: ApplicationStage
) => {
    const { collections } = await getDatabaseContext();

    const appStage = `${getPathByStage(stage)}.status`;

    const applicationSuiteIds = uniqueObjectIds(applications.map(item => item._versioning.suiteId).filter(Boolean));
    if (!applicationSuiteIds.length) {
        return {};
    }

    const filter = {
        '_versioning.suiteId': { $in: applicationSuiteIds },
        '_versioning.isLatest': true,
        [appStage]: { $in: [ApplicationStatus.Approved, ApplicationStatus.Declined] },
    };

    const queryApplications = await collections.applications.find(filter).toArray();

    const auditTrails = (await collections.auditTrails
        .find({
            _kind: {
                $in: [AuditTrailKind.ApplicationApproved, AuditTrailKind.ApplicationDeclined],
            },
            applicationSuiteId: { $in: queryApplications.map(query => query._versioning.suiteId) },
        })
        .toArray()) as (ApplicationApprovedAuditTrail | ApplicationDeclinedAuditTrail)[];

    const auditTrailByApplicationId = groupBy(item => item.applicationSuiteId.toHexString(), auditTrails);

    return auditTrailByApplicationId;
};

const submittedAuditTrailByApplicationSuiteIdFromApplications = async (
    applications: Application[],
    stage: ApplicationStage
) => {
    const { collections } = await getDatabaseContext();

    const applicationSuiteIds = uniqueObjectIds(applications.map(item => item._versioning.suiteId).filter(Boolean));
    if (!applicationSuiteIds.length) {
        return {};
    }

    const auditTrails = (await collections.auditTrails
        .find({
            _kind: {
                $in: [
                    // For internal applications, except mobility
                    AuditTrailKind.ApplicationSubmittedToSystem,
                    AuditTrailKind.ApplicationResubmittedToSystem,

                    // For mobility
                    AuditTrailKind.BookingSubmitted,
                    AuditTrailKind.BookingAmended,

                    // For SDM and BMW
                    AuditTrailKind.ApplicationSubmittedToBank,
                    AuditTrailKind.ApplicationResubmittedToBank,
                ],
            },
            stages: { $in: [stage] },
            applicationSuiteId: { $in: applicationSuiteIds },
        })
        .toArray()) as (
        | ApplicationSubmittedToSystemAuditTrail
        | ApplicationResubmittedToSystemAuditTrail
        | ApplicationSubmittedToBankAuditTrail
        | ApplicationResubmittedToBankAuditTrail
        | BookingSubmittedAuditTrail
        | BookingAmendedAuditTrail
    )[];

    // Using group by so it hold multiple values by suite id
    const auditTrailsByApplicationSuiteId = groupBy(item => item.applicationSuiteId.toHexString(), auditTrails);

    return auditTrailsByApplicationSuiteId;
};

const getConsentData = (journey: ApplicationJourney, consentById: { [key: string]: ConsentsAndDeclarations }) => {
    const consentData = {
        personal: CheckConsent.NotExist,
        email: CheckConsent.NotExist,
        fax: CheckConsent.NotExist,
        mail: CheckConsent.NotExist,
        phone: CheckConsent.NotExist,
        sms: CheckConsent.NotExist,
    };

    if (journey.applicantAgreements?.agreements?.length === 0) {
        return consentData;
    }
    const consentsLoaders = journey.applicantAgreements.agreements.map(
        agreement => consentById[agreement.consentId.toHexString()]
    );

    // get the first checkbox and Data processing checkbox
    const firstCheckBox = consentsLoaders.find(
        consent =>
            consent._type === ConsentsAndDeclarationsType.Checkbox && consent.dataField === DataField.DataProcessing
    );
    if (firstCheckBox) {
        const journeyFirstCheckBox = journey.applicantAgreements.agreements.find(agreement =>
            agreement.consentId.equals(firstCheckBox._id)
        );
        if (journeyFirstCheckBox) {
            consentData.personal = journeyFirstCheckBox.isAgreed ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    // get the first marketing consent in the journey
    const firstMarketingCheckbox = consentsLoaders.find(
        consent => consent._type === ConsentsAndDeclarationsType.Marketing
    );
    if (firstMarketingCheckbox) {
        const journeyFirstMarketing = journey.applicantAgreements.agreements.find(agreement =>
            agreement.consentId.equals(firstMarketingCheckbox._id)
        );

        if (journeyFirstMarketing?.platformsAgreed) {
            consentData.email = journeyFirstMarketing.platformsAgreed.email ? CheckConsent.Yes : CheckConsent.No;
            consentData.fax = journeyFirstMarketing.platformsAgreed.fax ? CheckConsent.Yes : CheckConsent.No;
            consentData.mail = journeyFirstMarketing.platformsAgreed.mail ? CheckConsent.Yes : CheckConsent.No;
            consentData.phone = journeyFirstMarketing.platformsAgreed.phone ? CheckConsent.Yes : CheckConsent.No;
            consentData.sms = journeyFirstMarketing.platformsAgreed.sms ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    return consentData;
};

export type GetConsentDataResult = ReturnType<typeof getConsentData>;

export const consentDataByApplicationVersionIdFromApplicationJourneys = async (
    applicationJourneys: ApplicationJourney[],
    loaders: Loaders
) => {
    const allConsentIds = applicationJourneys
        .flatMap(item => item.applicantAgreements?.agreements.map(agreement => agreement?.consentId) ?? [])
        .filter(Boolean);

    if (!allConsentIds.length) {
        return {};
    }

    const allConsents = await loaders.consentById.loadMany(allConsentIds).then(ensureManyFromLoaders);

    const consentById = keyBy(item => item._id.toHexString(), allConsents);

    return applicationJourneys
        .filter(item => item.applicantAgreements?.agreements?.length)
        .reduce(
            (acc, item) => ({
                ...acc,
                [item.applicationSuiteId.toHexString()]: getConsentData(item, consentById),
            }),
            {} as { [key: string]: GetConsentDataResult }
        );
};

const bankByIdFromApplications = async (applications: BankSupportedApplication[], loaders: Loaders) => {
    const bankIds = uniqueObjectIds(applications.map(item => item.bankId).filter(Boolean));
    const banks = await loaders.bankById.loadMany(bankIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), banks);
};

const financeProductByIdFromApplications = async (applications: AllowedApplicationForFinancing[], loaders: Loaders) => {
    const financeProductIds = uniqueObjectIds(
        applications.map(item => item.financing?.financeProductId).filter(Boolean)
    );

    if (!financeProductIds.length) {
        return {};
    }

    const financeProducts = await loaders.financeProductById.loadMany(financeProductIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), financeProducts);
};

const insurerByIdFromApplications = async (applications: AllowedApplicationForInsurance[], loaders: Loaders) => {
    const insurerIds = uniqueObjectIds(applications.map(item => item.insurancing?.insurerId).filter(Boolean));
    if (!insurerIds.length) {
        return {};
    }

    const insurers = await loaders.insurerById.loadMany(insurerIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), insurers);
};

const insuranceProductIdFromApplications = async (applications: AllowedApplicationForInsurance[], loaders: Loaders) => {
    const insuranceProductIds = uniqueObjectIds(
        applications.map(item => item.insurancing?.insuranceProductId).filter(Boolean)
    );

    if (!insuranceProductIds.length) {
        return {};
    }

    const insuranceProducts = await loaders.insuranceProductById
        .loadMany(insuranceProductIds)
        .then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), insuranceProducts);
};

export const promoCodeByIdFromApplication = async (applications: Application[], loaders: Loaders) => {
    const filteredApplication = applications.filter(({ kind }) =>
        [
            ApplicationKind.Event,
            ApplicationKind.Finder,
            ApplicationKind.Mobility,
            ApplicationKind.Standard,
            ApplicationKind.Configurator,
        ].includes(kind)
    ) as ApplicationHasPromoCode[];
    const promoCodeIds = uniqueObjectIds(filteredApplication.map(item => item.promoCodeId).filter(Boolean));
    if (!promoCodeIds.length) {
        return {};
    }
    const promoCodes = await loaders.promoCodeById.loadMany(promoCodeIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), promoCodes);
};

export const giftVoucherByIdFromApplication = async (applications: Application[], loaders: Loaders) => {
    const filteredApplication = applications.filter(({ kind }) =>
        [ApplicationKind.Mobility].includes(kind)
    ) as MobilityApplication[];
    const giftVoucherIds = uniqueObjectIds(filteredApplication.map(item => item.giftVoucherSuiteId).filter(Boolean));
    if (!giftVoucherIds.length) {
        return {};
    }
    const giftVouchers = await loaders.giftVoucherBySuiteId.loadMany(giftVoucherIds).then(ensureManyFromLoaders);

    return keyBy(item => item._versioning.suiteId.toHexString(), giftVouchers);
};

const getAuthorName = (
    author: Author,
    t: TFunction,
    {
        bankById,
        userById,
        insurerById,
        customerById,
        customerByLatestSuiteId,
        company,
        customerModuleByModuleId,
    }: {
        bankById?: Dictionary<Bank>;
        userById?: Dictionary<User>;
        insurerById: Dictionary<Insurer>;
        customerById: Dictionary<Customer>;
        customerByLatestSuiteId?: Dictionary<Customer>;
        customerModuleByModuleId: Dictionary<LocalCustomerManagementModule>;
        company: Company;
    }
) => {
    switch (author.kind) {
        case AuthorKind.Bank: {
            const bank = bankById?.[author.id.toHexString()];

            return bank?.kind === BankKind.System ? bank.legalName.defaultValue : bank?.displayName;
        }

        case AuthorKind.Insurer:
            return insurerById?.[author.id.toHexString()]?.legalName.defaultValue ?? '';

        case AuthorKind.Customer: {
            const customer = customerById?.[author.id.toHexString()];

            const customerModule = customerModuleByModuleId?.[customer.moduleId.toHexString()];

            if (!customer) {
                return '';
            }

            const latestCustomer =
                !customer._versioning.isLatest && customerByLatestSuiteId?.[customer._versioning.suiteId.toHexString()]
                    ? customerByLatestSuiteId[customer._versioning.suiteId.toHexString()]
                    : customer;

            const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

            return latestCustomer ? getCustomerFullName(t, latestCustomer, company, kycPresets) : '';
        }

        case AuthorKind.User:
            return userById?.[author.id.toHexString()]?.displayName ?? '';

        case AuthorKind.System:
            return 'System';

        default:
            throw new Error('Author name is not implemented');
    }
};

const companyByModuleIdFromModules = async (modules: SystemSupportedApplicationModule[], loaders: Loaders) => {
    if (!modules?.length) {
        return {};
    }

    const companyIds = uniqueObjectIds(modules.map(module => module.companyId));
    if (!companyIds.length) {
        return {};
    }

    const companies = await loaders.companyById.loadMany(companyIds).then(ensureManyFromLoaders);

    const companyById = keyBy(item => item._id.toHexString(), companies);

    return modules.reduce(
        (acc, module) => ({
            ...acc,
            [module._id.toHexString()]: companyById[module.companyId.toHexString()],
        }),
        {} as Dictionary<Company>
    );
};

const getSystemSupportingData = async (
    applications: Application[],
    modules: SystemSupportedApplicationModule[],
    stage: ApplicationStage,
    loaders: Loaders
) => {
    const [
        journeyMap,
        userById,
        submittedAuditTrailsByApplicationSuiteId,
        applicationApproval,
        referenceApplications,
        promoCodes,
        companyByModuleId,
        giftVouchers,
    ] = await Promise.all([
        journeyByApplicationSuiteIdFromApplications(applications, loaders),
        userByIdFromApplications(applications, loaders),
        submittedAuditTrailByApplicationSuiteIdFromApplications(applications, stage),
        applicationApprovalByApplicationSuiteIdFromApplications(applications, stage),
        referenceApplicationsByApplicationsSuiteId(applications),
        promoCodeByIdFromApplication(applications, loaders),
        companyByModuleIdFromModules(modules, loaders),
        giftVoucherByIdFromApplication(applications, loaders),
    ]);

    const consentDataByApplicationSuiteId = await consentDataByApplicationVersionIdFromApplicationJourneys(
        journeyMap.journeys,
        loaders
    );

    const [leadById, dealerById, vehicleById, modelMakeByVariantId] = await (async (): Promise<
        [
            Dictionary<WithId<Lead>> | null,
            Dictionary<WithId<Dealer>> | null,
            Dictionary<WithId<Vehicle>> | null,
            Dictionary<{
                model: LocalModel | null;
                subModel: LocalModel | null;
                make: LocalMake | null;
            }> | null,
        ]
    > =>
        Promise.all([
            leadByIdFromApplications(applications, loaders),
            dealerByIdFromApplications(applications as DealerSupportedApplication[], loaders),
            vehicleByIdFromApplications(applications as VehicleSupportedApplication[], loaders),
            modelMakeByVariantIdFromApplications(applications as VehicleSupportedApplication[], loaders),
        ]))();

    const { customerById, customerByLatestSuiteId, customerModuleByModuleId } = await customerByIdFromApplications(
        applications as KycSupportedApplication[],
        loaders
    );

    const bankById =
        modules.some(module => module._type !== ModuleType.MobilityModule) && stage === ApplicationStage.Financing
            ? await bankByIdFromApplications(applications as BankSupportedApplication[], loaders)
            : null;

    const financeProductById =
        stage === ApplicationStage.Financing
            ? await financeProductByIdFromApplications(applications as AllowedApplicationForFinancing[], loaders)
            : null;

    const insurerById =
        stage === ApplicationStage.Insurance
            ? await insurerByIdFromApplications(applications as AllowedApplicationForInsurance[], loaders)
            : null;

    const insuranceProductById =
        stage === ApplicationStage.Insurance
            ? await insuranceProductIdFromApplications(applications as AllowedApplicationForInsurance[], loaders)
            : null;

    return {
        userById,
        journeyByApplicationSuiteId: journeyMap.journeyByApplicationSuiteId,
        consentDataByApplicationSuiteId,
        vehicleById,
        modelMakeByVariantId,
        dealerById,
        customerById,
        customerByLatestSuiteId,
        bankById,
        financeProductById,
        insurerById,
        submittedAuditTrailsByApplicationSuiteId,
        applicationApproval,
        referenceApplications,
        promoCodes,
        companyByModuleId,
        giftVouchers,
        customerModuleByModuleId,
        insuranceProductById,
        leadById,
    };
};

export const prepareSystemApplicationData = async (
    applications: Application[],
    modules: ApplicationModule[],
    stage: ApplicationStage,
    t: TFunction,
    loaders = createLoaders()
): Promise<{
    preparedData: PreparedSystemApplicationData[];
    hasAppointment: boolean;
    hasAffinBankAutoFinanceCentre: boolean;
}> => {
    const {
        userById,
        journeyByApplicationSuiteId,
        consentDataByApplicationSuiteId,
        vehicleById,
        modelMakeByVariantId,
        dealerById,
        customerById,
        customerByLatestSuiteId,
        bankById,
        financeProductById,
        insurerById,
        submittedAuditTrailsByApplicationSuiteId,
        applicationApproval,
        referenceApplications,
        promoCodes,
        companyByModuleId,
        giftVouchers,
        customerModuleByModuleId,
        insuranceProductById,
        leadById,
    } = await getSystemSupportingData(applications, modules, stage, loaders);

    let hasAppointment = false;
    let hasAffinBankAutoFinanceCentre = false;

    return {
        preparedData: applications.map((item): PreparedSystemApplicationData => {
            const lead = leadById?.[item.leadId?.toHexString()] ?? null;

            // Get customer
            const customer = (() => {
                const foundCustomer = customerById?.[item.applicantId.toHexString()] ?? null;

                return foundCustomer ? getLocalCustomerAggregatedFields(foundCustomer) : null;
            })();

            if (!hasAppointment && item.kind !== ApplicationKind.SalesOffer && hasAppointmentScenario(item.scenarios)) {
                hasAppointment = true;
            }

            const bank =
                item.kind !== ApplicationKind.Mobility &&
                item.kind !== ApplicationKind.Launchpad &&
                item.kind !== ApplicationKind.SalesOffer &&
                item.bankId?.toHexString()
                    ? (bankById?.[item.bankId.toHexString()] ?? null)
                    : null;

            const [vehicle, vehicleInfo, dealer] = (() => [
                item.vehicleId?.toHexString() ? vehicleById?.[item.vehicleId.toHexString()] : null,
                item.vehicleId?.toHexString() ? modelMakeByVariantId?.[item.vehicleId.toHexString()] : null,
                item.dealerId?.toHexString() ? dealerById?.[item.dealerId.toHexString()] : null,
            ])();

            const financeProduct =
                (item.kind === ApplicationKind.Standard ||
                    item.kind === ApplicationKind.Finder ||
                    item.kind === ApplicationKind.Configurator ||
                    item.kind === ApplicationKind.Event) &&
                item.financing?.financeProductId?.toHexString()
                    ? (financeProductById?.[item.financing.financeProductId.toHexString()] ?? null)
                    : null;

            hasAffinBankAutoFinanceCentre =
                hasAffinBankAutoFinanceCentre ||
                ((item.kind === ApplicationKind.Standard ||
                    item.kind === ApplicationKind.Finder ||
                    item.kind === ApplicationKind.Configurator ||
                    item.kind === ApplicationKind.Event) &&
                    !!item.financing?.affinAutoFinanceCentre);

            const insurer =
                (item.kind === ApplicationKind.Standard ||
                    item.kind === ApplicationKind.Finder ||
                    item.kind === ApplicationKind.Configurator) &&
                item.insurancing?.insurerId?.toHexString()
                    ? (insurerById?.[item.insurancing.insurerId.toHexString()] ?? null)
                    : null;

            const insuranceProduct =
                (item.kind === ApplicationKind.Standard ||
                    item.kind === ApplicationKind.Finder ||
                    item.kind === ApplicationKind.Configurator) &&
                item.insurancing?.insurerId?.toHexString()
                    ? (insuranceProductById?.[item.insurancing?.insuranceProductId.toHexString()] ?? null)
                    : null;

            const assigneeId = getApplicationAssigneeId(item, lead, stage);
            const promoCode =
                item.kind === ApplicationKind.Mobility ||
                item.kind === ApplicationKind.Standard ||
                item.kind === ApplicationKind.Event ||
                item.kind === ApplicationKind.Configurator ||
                item.kind === ApplicationKind.Finder
                    ? promoCodes?.[item?.promoCodeId?.toHexString()]
                    : null;

            const giftVoucher =
                item.kind === ApplicationKind.Mobility ? giftVouchers?.[item.giftVoucherSuiteId?.toHexString()] : null;
            const company = companyByModuleId[item.moduleId.toHexString()];

            const capValues =
                item.kind !== ApplicationKind.Mobility ? leadById[item.leadId?.toHexString()]?.capValues : null;

            const campaignValues = item.kind === ApplicationKind.Event ? item.campaignValues : null;

            return {
                ...item,
                support: {
                    customer,
                    company,
                    assignee: assigneeId ? userById?.[assigneeId.toHexString()] : null,
                    creatorName: item._versioning.createdBy
                        ? getAuthorName(item._versioning.createdBy, t, {
                              bankById,
                              userById,
                              insurerById,
                              customerById,
                              customerByLatestSuiteId,
                              company,
                              customerModuleByModuleId,
                          })
                        : null,
                    editorName: item._versioning.updatedBy
                        ? getAuthorName(item._versioning.updatedBy, t, {
                              bankById,
                              userById,
                              insurerById,
                              customerById,
                              customerByLatestSuiteId,
                              company,
                              customerModuleByModuleId,
                          })
                        : null,
                    journey: journeyByApplicationSuiteId?.[item._versioning.suiteId.toHexString()],
                    consentData: consentDataByApplicationSuiteId?.[item._versioning.suiteId.toHexString()],
                    submittedAuditTrails:
                        submittedAuditTrailsByApplicationSuiteId?.[item._versioning.suiteId.toHexString()],
                    module: modules.find(module => module._id.equals(item.moduleId)),
                    applicationApproval: applicationApproval?.[item?._versioning.suiteId.toHexString()],
                    referenceApplications: referenceApplications?.[item?._versioning.suiteId.toHexString()],
                    appStage: getApplicationStage(item, lead, stage),
                    bank,
                    financeProduct,
                    vehicle,
                    insurer,
                    dealer,
                    model: vehicleInfo?.model,
                    subModel: vehicleInfo?.subModel,
                    make: vehicleInfo?.make,
                    promoCode,
                    giftVoucher,
                    insuranceProduct,
                    capValues,
                    campaignValues,
                },
            };
        }),
        hasAppointment,
        hasAffinBankAutoFinanceCentre,
    };
};
