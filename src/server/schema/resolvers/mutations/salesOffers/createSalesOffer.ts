import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationStage,
    LaunchPadModule,
    SalesOffer,
    SalesOfferDepositMethod,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
    SalesOfferModule,
    VehicleDataWithPorscheCodeIntegrationSetting,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
// eslint-disable-next-line max-len
import fetchVehicleDataWithPorscheCode from '../../../../integrations/porscheVehicleData/fetchVehicleDataWithPorscheCode';
// eslint-disable-next-line max-len
import retrievePorscheCodeLocalVariant from '../../../../integrations/porscheVehicleData/retrievePorscheCodeLocalVariant';
import { LeadPolicyAction } from '../../../../permissions';
import { initialFinancing, initialInsurance } from '../../../../utils/calculator';
import { getDefaultMarketValue } from '../../../../utils/calculator/shared/functions';
import { SingaporeMarketValue } from '../../../../utils/calculator/shared/typings';
import { getSimpleVersioningByUserForCreation, getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ApplicationKind, ApplicationMarket, GraphQLMutationResolvers, ModuleType } from '../../definitions';
import { getVsaSerialNumber } from './helpers';
import defaultPorscheVehicleApiVariable from './shared';

const defaultFeatureEmpty = {
    lastUpdatedAt: new Date(),
    status: SalesOfferFeatureStatus.Updated,
    documents: [],
    isEnabled: false,
};

const createDefaultFeature = <Kind extends SalesOfferFeatureKind>(
    kind: Kind,
    isEnabled: boolean = false,
    additionalFields: any = {}
) => ({
    ...defaultFeatureEmpty,
    isEnabled,
    kind,
    ...additionalFields,
});

const mutate: GraphQLMutationResolvers['createSalesOffer'] = async (
    root,
    { porscheCode, salesOfferModuleId, leadSuiteId },
    { loaders, getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const salesOfferModule = (await collections.modules.findOne({
        _id: salesOfferModuleId,
    })) as SalesOfferModule;

    const user = await getUser();

    if (!salesOfferModule || salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Sales offer module not found');
    }

    const existedLead = await collections.leads.findOne({
        '_versioning.suiteId': leadSuiteId,
        '_versioning.isLatest': true,
    });

    const launchpadModule = (await collections.modules.findOne({
        _id: existedLead.moduleId,
    })) as LaunchPadModule;

    const createSalesOfferPermission = permissionController.leads.mayOperateOn(
        existedLead,
        LeadPolicyAction.CreateSalesOffer
    );
    if (!createSalesOfferPermission) {
        throw new InvalidPermission();
    }

    const { companyId } = salesOfferModule;
    const company = await collections.companies.findOne({
        _id: companyId,
    });
    if (!company) {
        throw new Error('Company not found');
    }
    const setting = (await collections.settings.findOne({
        _id: salesOfferModule.vehicleDataWithPorscheCodeIntegrationSettingId,
    })) as VehicleDataWithPorscheCodeIntegrationSetting;

    const data = await fetchVehicleDataWithPorscheCode(
        porscheCode,
        defaultPorscheVehicleApiVariable.languageTags,
        defaultPorscheVehicleApiVariable.environment,
        defaultPorscheVehicleApiVariable.format,
        defaultPorscheVehicleApiVariable.width,
        defaultPorscheVehicleApiVariable.height,
        defaultPorscheVehicleApiVariable.top,
        defaultPorscheVehicleApiVariable.background,
        defaultPorscheVehicleApiVariable.marketplace,
        setting
    );

    if (!data) {
        throw new Error('No Porsche Vehicle Data found');
    }

    const { orderTypeCode } = data.vehicleDataForCustomerSpecifiedVehicle;
    const { vehicleId } = await retrievePorscheCodeLocalVariant(
        data.vehicleDataForCustomerSpecifiedVehicle.orderTypeCode,
        launchpadModule.vehicleModuleId
    );

    const variant = await collections.vehicles.findOne({
        _id: vehicleId,
    });

    if (!variant) {
        throw new Error('Variant not found');
    }

    const dealer = await collections.dealers.findOne({
        _id: existedLead.dealerId,
    });
    if (!dealer) {
        throw new Error('Dealer not found');
    }

    const marketType = getDefaultMarketValue(salesOfferModule.market.type, company, dealer);
    const parameters = {
        leadSuiteId,
        loaders,
        variantId: vehicleId,
        salesOfferModuleId,
        prices: {
            locallyFittedOptions: 0,
            optionsSubsidy: 0,
            vehiclePrice:
                data.vehicleDataForCustomerSpecifiedVehicle.vehicleBasicData.retailPrice.totalPrice.raw.marketDefault,
        },
    };

    const leadResults = await collections.leads.find({ '_versioning.suiteId': leadSuiteId }).toArray();
    const leadIds = leadResults.map(lead => lead._id);
    const tradeInApplication = await collections.applications.findOne({
        kind: ApplicationKind.Launchpad,
        stages: { $in: [ApplicationStage.TradeIn] },
        '_versioning.isLatest': true,
        leadId: { $in: leadIds },
    });

    try {
        const financing = await initialFinancing(parameters);
        const insurance = await initialInsurance(parameters);

        const salesOffer: SalesOffer = {
            _id: new ObjectId(),
            vehicle: createDefaultFeature(SalesOfferFeatureKind.Vehicle, true, {
                vehicleId,
                resultAPI: data,
                orderTypeCode,
                localFittedOptions: [],
            }),
            moduleId: salesOfferModuleId,
            leadSuiteId,
            porscheCode,
            vsaSerialNumber: await getVsaSerialNumber(company, salesOfferModule.vsaCounter),
            vsa: createDefaultFeature(SalesOfferFeatureKind.VSA, true),
            deposit: createDefaultFeature(SalesOfferFeatureKind.Deposit, true, {
                depositMethod: SalesOfferDepositMethod.Offline,
            }),
            tradeIn: createDefaultFeature(SalesOfferFeatureKind.TradeIn, !!tradeInApplication),
            mainDetails: createDefaultFeature(SalesOfferFeatureKind.MainDetails, true, {
                optionsSubsidy: 0,
                coeAmount:
                    marketType.market !== ApplicationMarket.Singapore
                        ? undefined
                        : (marketType as SingaporeMarketValue).coe,
            }),
            finance: createDefaultFeature(SalesOfferFeatureKind.Finance, !isNil(financing), { finance: financing }),
            insurance: createDefaultFeature(SalesOfferFeatureKind.Insurance, !isNil(insurance), {
                insurance,
            }),
            otherDocuments: [],
            _versioning: getSimpleVersioningByUserForCreation(user._id),
        };

        await collections.salesOffers.insertOne(salesOffer);

        const lead = await collections.leads.findOne({
            '_versioning.suiteId': leadSuiteId,
            '_versioning.isLatest': true,
        });

        await collections.leads.findOneAndUpdate(
            { _id: lead._id },
            { $set: { '_versioning.isLatest': false, ...getSimpleVersioningByUserForUpdate(user._id) } }
        );

        await collections.leads.insertOne({
            ...lead,
            _versioning: {
                ...lead._versioning,
                ...getSimpleVersioningByUserForUpdate(user._id),
            },
            _id: new ObjectId(),
            kind: ApplicationKind.Launchpad,
            salesOfferId: salesOffer._id,
        });

        return salesOffer;
    } catch (error) {
        throw new Error('Failed to create sales offer');
    }
};

export default requiresLoggedUser(mutate);
