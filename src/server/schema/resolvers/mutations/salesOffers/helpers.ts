import { uniq } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationJourneyKYC,
    ApplicationJourneyKYCKind,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    Collections,
    Company,
    CounterSettings,
    ExternalLinkKind,
    getKYCPresetsForSalesOffer,
    increaseCompanyCounter,
    KYCPreset,
    LaunchpadLead,
    LaunchPadModule,
    ModuleType,
    parseCounterPrefix,
    SalesOffer,
    SalesOfferAgreementKind,
    SalesOfferApplication,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
    SalesOfferModule,
    SendSalesOfferLink,
    User,
} from '../../../../database';
import { getApplicationIdentifierFromApplicationModule } from '../../../../database/helpers/applications/shared';
import createLoaders from '../../../../loaders';
import { ApplicationCancelSource, mainQueue } from '../../../../queues';
import { getApplicationLogStages } from '../../../../utils/application';
import { getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { AgreedConsentsJourney } from './shared';

const getAdditionalFields = async (
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance,
    existedLead: LaunchpadLead,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    collections: Collections
) => {
    switch (featureKind) {
        case SalesOfferFeatureKind.Deposit:
            return {
                stages: [ApplicationStage.Reservation],
                reservationStage: {
                    // reservation identifier will generate in draft step in sales offer journey
                    identifier: '',
                    status: ApplicationStatus.Drafted,
                    assigneeId: existedLead.assigneeId,
                },

                configuration: {
                    withFinancing: false,
                    withInsurance: false,
                },
            };
        case SalesOfferFeatureKind.Finance: {
            const financeProduct = await collections.financeProducts.findOne({
                _id: salesOffer.finance.finance.financeProductId,
            });

            return {
                stages: [ApplicationStage.Financing],
                financingStage: {
                    identifier: await getApplicationIdentifierFromApplicationModule(
                        ApplicationStage.Financing,
                        salesOfferModule
                    ),
                    status: ApplicationStatus.Drafted,
                    assigneeId: existedLead.assigneeId,
                },

                financing: salesOffer.finance.finance,

                bankId: financeProduct?.bankId,

                configuration: {
                    withFinancing: true,
                    withInsurance: false,
                },
            };
        }
        case SalesOfferFeatureKind.Insurance:
            return {
                stages: [ApplicationStage.Insurance],
                insuranceStage: {
                    identifier: await getApplicationIdentifierFromApplicationModule(
                        ApplicationStage.Insurance,
                        salesOfferModule
                    ),
                    status: ApplicationStatus.Drafted,
                    assigneeId: existedLead.assigneeId,
                },

                configuration: {
                    withFinancing: false,
                    withInsurance: true,
                },

                insurancing: salesOffer.insurance.insurance,
            };
        default:
            return {};
    }
};

const generateSalesOfferApplication = (
    {
        salesOfferModule,
        existedLead,
        salesOffer,
        origins,
        languageId,
        user,
    }: {
        salesOfferModule: SalesOfferModule;
        existedLead: LaunchpadLead;
        salesOffer: SalesOffer;
        origins;
        languageId: ObjectId;
        user: User;
    },
    additionalFields: any
) => ({
    _id: new ObjectId(),
    kind: ApplicationKind.SalesOffer,
    moduleId: salesOfferModule._id,
    // the application should be treated as a draft if there's no remote link sent
    isDraft: true,

    // define relationships
    applicantId: existedLead.customerId,
    vehicleId: salesOffer.vehicle.vehicleId,
    dealerId: existedLead.dealerId,

    // start with empty remarks and no document
    remarks: '',
    documents: [],
    // spread origins
    ...origins,

    // provide initial versioning
    _versioning: getAdvancedVersioningForCreation({ kind: AuthorKind.User, userId: user._id, date: new Date() }),
    languageId,

    leadId: existedLead._id,

    ...additionalFields,
});

const mapFeatureKindConsent = (featureKind: SalesOfferFeatureKind): keyof AgreedConsentsJourney => {
    switch (featureKind) {
        case SalesOfferFeatureKind.VSA:
            return 'salesOfferAgreedConsent';

        case SalesOfferFeatureKind.MainDetails:
            return 'coeAgreedConsent';

        case SalesOfferFeatureKind.Vehicle:
            return 'specificationAgreedConsent';

        case SalesOfferFeatureKind.Finance:
            return 'financeAgreedConsent';

        case SalesOfferFeatureKind.Insurance:
            return 'insuranceAgreedConsent';

        default:
            throw new Error('Invalid feature kind');
    }
};

const mapKYCIdsBasedOnSalesOfferFeatureKind = async (
    featureKind: SalesOfferFeatureKind,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    launchpadModule: LaunchPadModule,
    loaders = createLoaders()
): Promise<KYCPreset[]> => {
    switch (featureKind) {
        case SalesOfferFeatureKind.VSA:
            return getKYCPresetsForSalesOffer(
                salesOffer,
                salesOfferModule,
                launchpadModule,
                featureKind,
                SalesOfferAgreementKind.VSA,
                loaders
            );

        case SalesOfferFeatureKind.MainDetails:
            return getKYCPresetsForSalesOffer(
                salesOffer,
                salesOfferModule,
                launchpadModule,
                featureKind,
                SalesOfferAgreementKind.COE,
                loaders
            );

        case SalesOfferFeatureKind.Vehicle:
            return getKYCPresetsForSalesOffer(
                salesOffer,
                salesOfferModule,
                launchpadModule,
                featureKind,
                SalesOfferAgreementKind.Specification,
                loaders
            );

        case SalesOfferFeatureKind.Finance:
        case SalesOfferFeatureKind.Insurance:
            return getKYCPresetsForSalesOffer(
                salesOffer,
                salesOfferModule,
                launchpadModule,
                featureKind,
                null,
                loaders
            );

        default:
            throw new Error('Invalid feature kind');
    }
};

const buildApplicationJourneyKYCConsent = async (
    featureKind: SalesOfferFeatureKind,
    consentChanges: AgreedConsentsJourney,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    launchpadModule: LaunchPadModule,
    lead: LaunchpadLead
) => {
    const featureConsent = consentChanges[mapFeatureKindConsent(featureKind)];

    const kycPresets = await mapKYCIdsBasedOnSalesOfferFeatureKind(
        featureKind,
        salesOffer,
        salesOfferModule,
        launchpadModule
    );

    const applicantKYC: ApplicationJourneyKYC = {
        completed: true,
        completedAt: new Date(),
        customerId: lead.customerId,
        kycIds: kycPresets.map(kyc => kyc._id),
        moduleId: launchpadModule._id,
        type: ApplicationJourneyKYCKind.LocalCustomer,
    };

    return {
        applicantKYC,
        applicantAgreements: { moduleId: launchpadModule.agreementsModuleId, agreements: featureConsent },
    };
};

const generateSalesOfferApplicationJourney = async (
    application: SalesOfferApplication,
    consentChanges: AgreedConsentsJourney,
    lead: LaunchpadLead,
    featureKind: SalesOfferFeatureKind,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    launchpadModule: LaunchPadModule
): Promise<ApplicationJourney> => {
    const { applicantAgreements, applicantKYC } = await buildApplicationJourneyKYCConsent(
        featureKind,
        consentChanges,
        salesOffer,
        salesOfferModule,
        launchpadModule,
        lead
    );

    return {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: new Date(),
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
        applicantKYC,
        applicantAgreements,
    };
};

export const getApplicationSuiteIdKey = (
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance
) => {
    switch (featureKind) {
        case SalesOfferFeatureKind.Deposit:
            return 'latestReservationApplicationSuiteId';

        case SalesOfferFeatureKind.Finance:
            return 'latestFinancingApplicationSuiteId';

        case SalesOfferFeatureKind.Insurance:
            return 'latestInsuranceApplicationSuiteId';

        default:
            throw new Error('Invalid feature kind');
    }
};

const getLatestApplicationSuiteId = async (
    {
        salesOfferModule,
        existedLead,
        salesOffer,
        origins,
        languageId,
        user,
        collections,
        consentChanges,
        launchpadModule,
    }: {
        salesOfferModule: SalesOfferModule;
        existedLead: LaunchpadLead;
        salesOffer: SalesOffer;
        origins;
        languageId: ObjectId;
        user: User;
        collections: Collections;
        consentChanges: AgreedConsentsJourney;
        launchpadModule: LaunchPadModule;
    },
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance
) => {
    const getNewSalesOfferApplicationId = async () => {
        const application: SalesOfferApplication = generateSalesOfferApplication(
            {
                salesOfferModule,
                existedLead,
                salesOffer,
                origins,
                languageId,
                user,
            },
            await getAdditionalFields(featureKind, existedLead, salesOffer, salesOfferModule, collections)
        );
        const applicationJourney: ApplicationJourney = await generateSalesOfferApplicationJourney(
            application,
            consentChanges,
            existedLead,
            featureKind,
            salesOffer,
            salesOfferModule,
            launchpadModule
        );
        // insert the documents in database
        await collections.applications.insertOne(application);
        await collections.applicationJourneys.insertOne(applicationJourney);

        // generate the trail
        const trail: ApplicationDraftedAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationDrafted,
            _date: new Date(),
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
            author: getAuthorFromAuthoring({ kind: AuthorKind.User, userId: user._id, date: new Date() }),
        };

        await collections.auditTrails.insertOne(trail);

        return application._versioning.suiteId;
    };

    const applicationSuiteIdKey = getApplicationSuiteIdKey(featureKind);

    if (salesOffer[applicationSuiteIdKey]) {
        const application = await collections.applications.findOne<SalesOfferApplication>({
            '_versioning.suiteId': salesOffer[applicationSuiteIdKey],
            '_versioning.isLatest': true,
        });

        if (application) {
            const applicationJourney = await collections.applicationJourneys.findOne<ApplicationJourney>({
                applicationSuiteId: salesOffer[applicationSuiteIdKey],
            });

            /**
             * if existing application is received, we need to cancel it and create a new one
             * if existing application is not received, we can just return the existing one
             */
            if (applicationJourney.isReceived) {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.User,
                    applicationId: application._id,
                    userId: user._id,
                    stage: ApplicationStage.Reservation,
                });

                return getNewSalesOfferApplicationId();
            }

            const launchpadModule = await collections.modules.findOne({ _id: existedLead.moduleId });
            if (!launchpadModule || launchpadModule._type !== ModuleType.LaunchPadModule) {
                throw new Error('Launchpad module not found or invalid type');
            }

            const { applicantAgreements, applicantKYC } = await buildApplicationJourneyKYCConsent(
                featureKind,
                consentChanges,
                salesOffer,
                salesOfferModule,
                launchpadModule,
                existedLead
            );

            // update the financing application with the new sales offer financing
            if (featureKind === SalesOfferFeatureKind.Finance) {
                await collections.applications.findOneAndUpdate(
                    { '_versioning.suiteId': salesOffer[applicationSuiteIdKey], '_versioning.isLatest': true },
                    {
                        $set: {
                            financing: salesOffer.finance.finance,
                            applicantId: existedLead.customerId,
                        },
                    }
                );
            }

            // update the insurance application with the new sales offer insurance
            if (featureKind === SalesOfferFeatureKind.Insurance) {
                await collections.applications.findOneAndUpdate(
                    { '_versioning.suiteId': salesOffer[applicationSuiteIdKey], '_versioning.isLatest': true },
                    {
                        $set: {
                            insurancing: salesOffer.insurance.insurance,
                            applicantId: existedLead.customerId,
                        },
                    }
                );
            }

            await collections.applicationJourneys.findOneAndUpdate(
                { applicationSuiteId: salesOffer[applicationSuiteIdKey] },
                {
                    $set: {
                        applicantAgreements,
                        applicantKYC,
                    },
                }
            );

            return salesOffer[applicationSuiteIdKey];
        }
    }

    /**
     * otherwise we can just create a new one
     */
    return getNewSalesOfferApplicationId();
};

/**
 * if feature has existing application,
 * if application is submitted, we need to cancel it
 * if application is draft we need to delete it
 */
export const checkAndCancelOrDeleteApplication = async (
    {
        salesOffer,
        user,
        collections,
    }: {
        salesOffer: SalesOffer;
        user: User;
        collections: Collections;
    },
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance
) => {
    const applicationSuiteIdKey = getApplicationSuiteIdKey(featureKind);

    if (salesOffer[applicationSuiteIdKey]) {
        const application = await collections.applications.findOne<SalesOfferApplication>({
            '_versioning.suiteId': salesOffer[applicationSuiteIdKey],
            '_versioning.isLatest': true,
        });

        if (application) {
            const applicationJourney = await collections.applicationJourneys.findOne<ApplicationJourney>({
                applicationSuiteId: salesOffer[applicationSuiteIdKey],
            });

            if (applicationJourney.isReceived) {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.User,
                    applicationId: application._id,
                    userId: user._id,
                    stage: ApplicationStage.Reservation,
                });
            } else {
                await collections.applications.deleteMany({ '_versioning.suiteId': salesOffer[applicationSuiteIdKey] });
                await collections.applicationJourneys.deleteOne({
                    applicationSuiteId: application._versioning.suiteId,
                });
            }
        }
    }
};

const getFeaturePendingChanges = async (
    featureKind: SalesOfferFeatureKind,
    consentChanges: AgreedConsentsJourney,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    launchpadModule: LaunchPadModule,
    lead: LaunchpadLead
) => {
    const { applicantAgreements } = await buildApplicationJourneyKYCConsent(
        featureKind,
        consentChanges,
        salesOffer,
        salesOfferModule,
        launchpadModule,
        lead
    );

    switch (featureKind) {
        case SalesOfferFeatureKind.VSA:
            return {
                [`${featureKind}.status`]: SalesOfferFeatureStatus.PendingManager,
                [`${featureKind}.lastUpdatedAt`]: new Date(),
                'consents.salesOffer': applicantAgreements,
            };
        case SalesOfferFeatureKind.MainDetails:
            return {
                [`${featureKind}.status`]: SalesOfferFeatureStatus.PendingCustomer,
                [`${featureKind}.lastUpdatedAt`]: new Date(),
                'consents.coe': applicantAgreements,
            };
        case SalesOfferFeatureKind.Vehicle:
            return {
                [`${featureKind}.status`]: SalesOfferFeatureStatus.PendingCustomer,
                [`${featureKind}.lastUpdatedAt`]: new Date(),
                'consents.specifications': applicantAgreements,
            };
        case SalesOfferFeatureKind.Deposit:
        case SalesOfferFeatureKind.Finance:
        case SalesOfferFeatureKind.Insurance:
            return {
                [`${featureKind}.status`]: SalesOfferFeatureStatus.PendingCustomer,
                [`${featureKind}.lastUpdatedAt`]: new Date(),
            };

        default:
            throw new Error('Feature kind not supported');
    }
};

/**
 * prepare normal update for feature kinds
 * eg:  feature status and lastUpdatedAt and latest application id
 */
export const getSendingUpdates = async (
    {
        salesOfferModule,
        existedLead,
        salesOffer,
        origins,
        languageId,
        user,
        collections,
        consentChanges,
        launchpadModule,
    }: {
        salesOfferModule: SalesOfferModule;
        existedLead: LaunchpadLead;
        salesOffer: SalesOffer;
        origins;
        languageId: ObjectId;
        user: User;
        collections: Collections;
        consentChanges: AgreedConsentsJourney;
        launchpadModule: LaunchPadModule;
    },
    featureKinds: SalesOfferFeatureKind[]
) => {
    const getUpdate = async featureKind => {
        switch (featureKind) {
            case SalesOfferFeatureKind.VSA:
            case SalesOfferFeatureKind.MainDetails:
            case SalesOfferFeatureKind.Vehicle:
                return getFeaturePendingChanges(
                    featureKind,
                    consentChanges,
                    salesOffer,
                    salesOfferModule,
                    launchpadModule,
                    existedLead
                );

            case SalesOfferFeatureKind.Deposit:
            case SalesOfferFeatureKind.Finance:
            case SalesOfferFeatureKind.Insurance: {
                const latestApplicationSuiteId = await getLatestApplicationSuiteId(
                    {
                        salesOfferModule,
                        existedLead,
                        salesOffer,
                        origins,
                        languageId,
                        user,
                        collections,
                        consentChanges,
                        launchpadModule,
                    },
                    featureKind
                );

                return {
                    ...(await getFeaturePendingChanges(
                        featureKind,
                        consentChanges,
                        salesOffer,
                        salesOfferModule,
                        launchpadModule,
                        existedLead
                    )),
                    [getApplicationSuiteIdKey(featureKind)]: latestApplicationSuiteId,
                };
            }

            default:
                throw new Error('Feature kind not supported');
        }
    };

    const updates = (await Promise.all(featureKinds.map(featureKind => getUpdate(featureKind)))).reduce(
        (acc, curr) => ({ ...acc, ...curr }),
        {}
    );

    return updates;
};

/**
 * if feature has existing external link, we need to delete it
 * return impact feature kinds
 */
const checkAndDeleteExternalLink = async (
    salesOfferId: ObjectId,
    featureKind: SalesOfferFeatureKind,
    collections: Collections
): Promise<SalesOfferFeatureKind[]> => {
    const existingExternalLinks = await collections.externalLinks
        .find<SendSalesOfferLink>({
            _kind: ExternalLinkKind.SendSalesOffer,
            'data.featureKinds': { $in: [featureKind] },
            'data.salesOfferId': salesOfferId,
        })
        .toArray();

    let otherImpactedFeatureKinds = [];
    existingExternalLinks.forEach(i => {
        if (i.data.featureKinds.length > 1) {
            otherImpactedFeatureKinds = otherImpactedFeatureKinds.concat(
                i.data.featureKinds.filter(fk => fk !== featureKind)
            );
        }
    });

    const uniqFeatureKinds = uniq(otherImpactedFeatureKinds);

    const linkIds = existingExternalLinks.map(i => i._id);

    await collections.externalLinks.deleteMany({ _id: { $in: linkIds } });

    return uniqFeatureKinds;
};
/**
 * prepare impacted updates for feature kinds
 * eg
 * first action, send ['mainDetail', 'vehicle'], and customer did not touch the link
 * second action, send ['mainDetail']
 *
 * in second action, the impacted feature is ['vehicle']
 * so first we need to delete the external link
 * then we need update vehicle status to updated and lastUpdatedAt to now
 */
export const getImpactedUpdates = async (
    salesOffer: SalesOffer,
    featureKinds: SalesOfferFeatureKind[],
    collections: Collections,
    user: User
) => {
    const allImpactedFeatureKinds = (
        await Promise.all(
            featureKinds.map(featureKind => checkAndDeleteExternalLink(salesOffer._id, featureKind, collections))
        )
    ).reduce((acc, curr) => [...acc, ...curr], []);

    const allUniqImpactedFeatureKinds = uniq(allImpactedFeatureKinds);
    const realImpactedFeatureKinds = allUniqImpactedFeatureKinds.filter(kind => !featureKinds.includes(kind));

    const getUpdate = async featureKind => {
        const basicUpdates = {
            [`${featureKind}.status`]: SalesOfferFeatureStatus.Updated,
            [`${featureKind}.lastUpdatedAt`]: new Date(),
        };

        switch (featureKind) {
            case SalesOfferFeatureKind.VSA:
            case SalesOfferFeatureKind.MainDetails:
            case SalesOfferFeatureKind.Vehicle:
                return basicUpdates;

            case SalesOfferFeatureKind.Deposit:
            case SalesOfferFeatureKind.Finance:
            case SalesOfferFeatureKind.Insurance:
                await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, featureKind);

                return {
                    [getApplicationSuiteIdKey(featureKind)]: null,
                    ...basicUpdates,
                };

            default:
                throw new Error('Feature kind not supported');
        }
    };

    const impactedUpdates = (
        await Promise.all(realImpactedFeatureKinds.map(featureKind => getUpdate(featureKind)))
    ).reduce((acc, curr) => ({ ...acc, ...curr }), {});

    return impactedUpdates;
};

// generate the serial number for VSA from VSA counter
export const getVsaSerialNumber = async (company: Company, counter: CounterSettings) => {
    // get the index on the counter
    // the index is based on the raw prefix to ensure global appliance
    const index = await increaseCompanyCounter(company._id, counter.prefix, counter.method, company.timeZone);

    return [
        // first part is the computed prefix
        parseCounterPrefix(counter.prefix, company.timeZone),
        // second part is the allocated index with leading zeros
        index.toString().padStart(counter.padding + 1, '0'),
    ].join('');
};
